import MultiMaterial from "../../../core/component/MultiMaterial";
import { util } from "../../../core/utils/Utils";
import { ConditionType, EnergyRecoverType, ItemID } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { viewHelper } from "../../common/helper/ViewHelper";

const { ccclass } = cc._decorator;

@ccclass
export default class EnergyStationPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected descRt_: cc.RichText = null // path://root/desc_rt
    protected costNode_: cc.Node = null // path://root/cost_n
    protected freeNode_: cc.Node = null // path://root/free_n
    protected currencyLayoutNode_: cc.Node = null // path://currency_layout_n
    //@end

    private _updateReg: Function = null

    public listenEventMaps() {
        return [
            { [EventType.DAILY_REFRESH]: this.initView },
            { [EventType.ENERGY_SYNC]: this.initView },
        ]
    }

    public async onCreate() {
    }

    public onEnter(data: any) {
        this.initView()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://root/free_n/free_ok_be
    onClickFreeOk(event: cc.Event.EventTouch, data: string) {
        this.onOk(EnergyRecoverType.FREE)
    }

    // path://root/cost_n/cost_ok_be
    onClickCostOk(event: cc.Event.EventTouch, data: string) {
        this.onOk(EnergyRecoverType.ENERGY)
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------


    private initView() {
        const free = cfgHelper.getMiscData("speedUp").recoverFree
        this.descRt_.setLocaleKey("exploreSpeedUp_guiText_5", free)
        this.initCost()
        this.initFree()
        // this.initAd()
    }

    initCost() {
        let cur = gameHelper.bag.getPropCountById(ItemID.ENERGY)
        let need = 1
        const lbl: cc.Node = this.costNode_.Child("item/lbl")
        lbl.Component(cc.MultiColor).setColor(cur <= 0)
        lbl.getComponent(cc.Label).string = String(need)
        const btn = this.costNode_.Child("cost_ok_be", cc.Button)
        btn.interactable = cur > 0
        btn.Component(MultiMaterial).setMaterial(cur <= 0)
    }
    /**
     * @deprecated
     */
    initAd() {
        const lbl: cc.Node = this.freeNode_.Child("item/lbl")
        lbl.Component(cc.RichText).setLocaleUpdate(() => {
            let has = gameHelper.ad.getRemainTimes(proto.AdType.RecoveryTrainEnergy)
            let max = gameHelper.ad.getMaxTimes(proto.AdType.RecoveryTrainEnergy)
            return has + "/" + max
        })
    }

    initFree() {
        this._updateReg = null
        const item = this.freeNode_.Child("item")
        const time = this.freeNode_.Child("time")
        const btn = this.freeNode_.Child("free_ok_be", cc.Button)
        const has = gameHelper.world.energy.freeRecoverNum
        btn.interactable = has > 0
        item.active = time.active = false
        if (has <= 0) {
            time.active = true
            const lbl = time.Child("lbl", cc.Label)
            this._updateReg = () => lbl.string = ut.millisecondFormat(gameHelper.world.energy.getRecoverTime(), `hh:mm:ss`)
            return
        }
        item.active = true
        const lbl: cc.Node = item.Child("lbl")
        lbl.Component(cc.RichText).setLocaleUpdate(() => 1 + "/" + has)
    }

    private async onOk(type: EnergyRecoverType) {
        let err_tip = ""
        const energy = gameHelper.world.energy
        switch (type) {
            case EnergyRecoverType.AD:
                if (gameHelper.ad.getRemainTimes(proto.AdType.RecoveryTrainEnergy) <= 0) {
                    err_tip = "speedUpAuto_tip_1"
                } else {
                    const bol = await new Promise<boolean>((resolve, reject) => {
                        viewHelper.showWatchAd(proto.AdType.RecoveryTrainEnergy, (bol: boolean) => {
                            resolve(bol)
                        })
                    })
                    if (!bol) return
                }
                break
            case EnergyRecoverType.FREE:
                if (gameHelper.world.energy.freeRecoverNum <= 0) {
                    err_tip = "speedUpAuto_tip_2"
                }
                break
            case EnergyRecoverType.ENERGY:
                let cur = gameHelper.bag.getPropCountById(ItemID.ENERGY)
                let need = 1
                if (cur < need) {
                    err_tip = "speedUpAuto_tip_0"
                }
                break
        }

        if (err_tip) {
            return void viewHelper.showAlert(err_tip)
        }
        await energy.recoverEnergy(type) && this.close()
    }

    update(dt: number) {
        this._updateReg?.()
    }
}
