import { util } from "../../../core/utils/Utils";
import { ConditionType, ItemID } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { uiHelper } from "../../common/helper/UIHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import ConditionObj from "../../model/common/ConditionObj";

const { ccclass } = cc._decorator;

@ccclass
export default class EnergyBuyPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected descLbl_: cc.Label = null // path://root/desc_l
    protected costNode_: cc.Node = null // path://root/cost_n
    protected itemNode_: cc.Node = null // path://root/item_n
    protected currencyLayoutNode_: cc.Node = null // path://currency_layout_n
    //@end

    private isBuy: boolean = false
    private cb: Function = null
    private need: number = 0

    public listenEventMaps() {
        return [
            { [EventType.UPDATE_DIAMOND]: this.initView },
        ]
    }

    public onEnter(data: any) {
        this.cb = data

        let energy = gameHelper.world.energy
        let cur = gameHelper.bag.getPropCountById(ItemID.ENERGY)
        let cost = 0
        this.need = cost - cur

        this.initView()
    }

    public onRemove() {
        if (this.cb) {
            this.cb(this.isBuy)
        }
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://root/btnYes_be
    onClickBtnYes(event: cc.Event.EventTouch, data: string) {
        this.buy()
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    
    // ----------------------------------------- custom function ----------------------------------------------------

    private initView() {
        let need = this.need
        this.descLbl_.setLocaleKey("speedUp_guiText_3", need)

        let cond = new ConditionObj().init(ConditionType.PROP, ItemID.ENERGY, need)
        resHelper.loadIconByCondInfo(cond, this.itemNode_.Child('icon'), this.getTag())
        this.itemNode_.Child('num', cc.Label).string = uiHelper.getShowNum(cond)

        let rate = cfgHelper.getMiscData("speedUp").recoverEnergy
        let needDiamond = rate * need
        let lb = this.costNode_.Child('lb', cc.Label)
        lb.string = String(needDiamond)
        lb.Component(cc.MultiColor).setColor(gameHelper.getDiamond() < needDiamond)
    }

    @util.addLock
    private async buy() {
        let rate = cfgHelper.getMiscData("speedUp").recoverEnergy
        let needDiamond = rate * this.need
        let cond = new ConditionObj().init(ConditionType.DIAMOND, -1, needDiamond)
        
        if (!uiHelper.checkBuyCost([cond])) {
            return
        }

        let succ = await gameHelper.bag.buyBattery(this.need)
        if (!cc.isValid(this) || !succ) return

        this.isBuy = true
        await viewHelper.showGeneralReward([new ConditionObj().init(ConditionType.PROP, ItemID.ENERGY, this.need)])
        if (!cc.isValid(this)) return
        this.close()
    }
    
}
