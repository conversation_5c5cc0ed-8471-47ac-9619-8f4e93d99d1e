import { game<PERSON>el<PERSON> } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import EventType from "../../common/event/EventType";
import NodeType from "../../common/event/NodeType";
import ToolModel from "../../model/tool/ToolModel";
import { cfgHelper } from "../../common/helper/CfgHelper";
import Tool from "../../model/tool/Tool";
import FlashLightShaderCtrl from "../../common/shader/FlashLightShaderCtrl";
import ConditionObj from "../../model/common/ConditionObj";
import { uiHelper } from "../../common/helper/UIHelper";
import { TOOL_ATTR_CFG } from "../../common/constant/Constant";
import PlanetMineModel from "../../model/planet/PlanetMineModel";
import { PlanetMineCfg } from "../../common/constant/DataType";
import { HeroAnimation, LongPress, PassengerLifeAnimation, PlanetMineType } from "../../common/constant/Enums";
import PlanetMineCmpt from "../cmpt/planet/PlanetMineCmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class ToolMakePnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected titleLbl_: cc.Label = null // path://title/progress/title_l
    protected heroNode_: cc.Node = null // path://hero_n
    protected tipsNode_: cc.Node = null // path://tips_n
    protected bgNode_: cc.Node = null // path://table/bg_n
    protected lvLbl_: cc.Label = null // path://table/lv_l
    protected btnTableNode_: cc.Node = null // path://table/btnTable_n_be
    protected titleNode_: cc.Node = null // path://table/title_n
    protected costNode_: cc.Node = null // path://table/cost_n
    protected maxLvNode_: cc.Node = null // path://table/maxLv_n
    protected btnMakeNode_: cc.Node = null // path://table/btnMake_n_be
    protected tipsPosNode_: cc.Node = null // path://table/tipsPos_n
    protected currencyNode_: cc.Node = null // path://currency_n
    protected equipNode_: cc.Node = null // path://equip_n
    protected descNode_: cc.Node = null // path://desc_n
    protected backNode_: cc.Node = null // path://back_be_n
    //@end

    private model: ToolModel = null
    private selectType: number = PlanetMineType.TREE
    private lastFlowEquipNode: cc.Node = null
    private lockPressEvt: boolean = false

    public listenEventMaps() {
        return [
            { [NodeType.GUIDE_BUTTON_TOOL_BUILD]: () => this.btnMakeNode_ },
            { [NodeType.GUIDE_BUTTON_TOOLLIST_3]: () => this.getEquipItem(3) },
            { [NodeType.GUIDE_BUTTON_BACK_6]: () => this.backNode_ },
            { [EventType.TOOL_TABLE_UP]: this.refreshBg },
            { [EventType.TOOL_TABLE_UP]: this.refreshCenter },
            { [EventType.TOOL_TABLE_UP]: this.refreshCosts },
            { [EventType.TOOL_SELECT]: this.selectTool },
        ]
    }

    public async onCreate() {
    }

    public onEnter(id?: number) {
        this.model = gameHelper.tool
        this.initSelectType(id)
        this.initView()
        viewHelper.hidePnl("common/PlanetUI")
    }

    public onRemove() {
        viewHelper.showPnl("common/PlanetUI")
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://table/btnMake_n_be
    onClickBtnMake(event: cc.Event.EventTouch, data: string) {
        let failList: ConditionObj[] = []
        gameHelper.checkConditions(gameHelper.toConditions(this.model.getToolByType(this.selectType).cost), failList)
        if (failList.length > 0) {
            gameHelper.showFailTips(failList)
            return
        }
        let tableCfg = cfgHelper.getToolTableByLv(this.model.getLv())
        if (tableCfg?.maxToolLv > this.model.getToolByType(this.selectType).getLv()) {
            this.make()
        } else {
            viewHelper.showAlert(this.model.getLv() == cfgHelper.getMaxToolTableLv() ? "tool_tips_8" : "tool_tips_7")
        }
    }

    // path://table/btnTable_n_be
    onClickBtnTable(event: cc.Event.EventTouch, data: string) {
        //打开打造台升级界面
        viewHelper.showPnl('tool/ToolTable', {
            onLevelUpCallback: () => this.updateTitleMax()
        })
    }

    // path://back_be_n
    onClickBack(event: cc.Event.EventTouch, data: string) {
        this.close()
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    private async make() {
        let type = this.selectType
        let sk = this.btnMakeNode_.Child('hammer', sp.Skeleton)
        this.setToolBtn(false)
        this.btnMakeNode_.Child('lb').active = false
        this.btnMakeNode_.Component(cc.Button).interactable = false
        sk.playAnimation('dianji', false)
        let succ = await this.model.getToolByType(this.selectType).make()
        if (!cc.isValid(this)) return
        if (succ) {
            this.refreshCenter()
            await ut.wait(.8, this)
            if (!cc.isValid(this)) return
            let tool = this.model.getToolByType(type)
            if (tool?.getPreCfg()?.quality != tool.quality || tool.getPreSkinInfo()?.icon != tool.icon) {
                this.refreshEquip(type)
                this.refreshHero(type)
                this.showComplete(tool)
            } else {
                this.refreshEquip()
                this.showTips(tool)
            }
            this.refreshHammer()
            this.refreshCosts()
            this.playLvUpAnim(type)
        }
    }

    // ----------------------------------------- custom function ----------------------------------------------------

    private initView() {
        this.updateTitleMax()
        this.refreshBg()
        this.refreshCenter()
        this.refreshEquip()
        this.refreshHammer()
        this.refreshSelect()
        this.refreshCosts()
        this.refreshHero(this.selectType)
        this.heroNode_.y = -280 * viewHelper.getPlanetWindScale()
        resHelper.loadOwnRoleSp(1005, this.heroNode_.Component("hero"), this.getTag())
    }

    private refreshBg(needFlash: boolean = false) {
        let cfg = cfgHelper.getToolSkinCfg(this.model.getLv())
        let preCfg
        if (needFlash) {
            preCfg = cfgHelper.getToolSkinCfg(this.model.getLv() - 1)
        }
        let node = this.bgNode_.Child('up')
        node.children.forEach(it => it.active = false)
        for (let type in cfg) {
            node.Child(type).active = true
            if (type == 'drillBit') {
                resHelper.loadIcon(node.Child(`${type}/l`), "tool/table", cfg[type], this.getTag()).then(() => {
                    if (!cc.isValid(this)) return
                    if (needFlash && preCfg[type] != cfg[type]) this.flashPart(node.Child(`${type}/l`))
                })
                resHelper.loadIcon(node.Child(`${type}/r`), "tool/table", cfg[type], this.getTag()).then(() => {
                    if (!cc.isValid(this)) return
                    if (needFlash && preCfg[type] != cfg[type]) this.flashPart(node.Child(`${type}/r`))
                })
            } else {
                resHelper.loadIcon(node.Child(type), "tool/table", cfg[type], this.getTag()).then(() => {
                    if (!cc.isValid(this)) return
                    if (needFlash && preCfg[type] != cfg[type]) this.flashPart(node.Child(type))
                })
            }
        }
    }

    private refreshEquip(sort: number = -1) {
        let tools = this.getTools()
        this.equipNode_.Items(tools, (it, data, i) => {
            it.Data = data
            let icon = it.Child("icon")
            it.Child('bg', cc.MultiFrame).setFrame(data.quality - 1)
            if (!!data.getLv()) {
                icon.active = true
                it.Child('lv').active = true
                resHelper.loadIcon(icon, "tool/icon", data.icon, this.getTag()).then(() => {
                    if (!cc.isValid(this)) return
                    if (data.getType() == sort) {
                        this.flashEquip(sort)
                    }
                })
                it.Child('lv', cc.Label).setLocaleKey('tool_guiText_7', data.getLv())

            } else {
                icon.active = false
                it.Child('lv').active = false
            }
            const press = it.Child("press")
            press.off(LongPress.CLICK)
            press.on(LongPress.CLICK, () => {
                if (this.lockPressEvt) return
                this.selectTool(data.getType())
            })

            press.off(LongPress.LPSTART)
            press.on(LongPress.LPSTART, () => {
                if (this.lockPressEvt) return
                if (!data.getLv()) return void viewHelper.showAlert("ui_tool_no_equip_tip")
                // 第一个装备的y
                let lastY = -91.5
                if (this.lastFlowEquipNode) {
                    lastY = this.lastFlowEquipNode.y
                }
                lastY = Math.abs(lastY)
                const rate = lastY > it.y ? 1 : -1
                const curY = Math.abs(it.y)
                const diff = curY - lastY
                this.setDescItem(data)
                this.descNode_.y -= diff * rate
                this.lastFlowEquipNode = it
            })
            press.on(LongPress.LPEND, () => {
                this.descNode_.active = false
            })
        })
        this.setToolBtn()
    }

    private getEquipItem(type: PlanetMineType) {
        let ary = this.equipNode_.children
        return ary.find(it => (it.Data as Tool).getType() == type)
    }

    private flashEquip(sort: number) {
        if (!sort || sort <= 0) return
        let equip = this.equipNode_.children[sort - 1]
        if (!equip || !equip.Child("icon/val", cc.Sprite).spriteFrame) return
        equip.Child("icon/val", FlashLightShaderCtrl).play()
    }

    private flashPart(node: cc.Node) {
        if (!node || !node.Child('val', cc.Sprite).spriteFrame) return
        node.Child('val', FlashLightShaderCtrl).play()
    }

    private refreshCenter() {
        let model = this.model
        let lv = model.getLv()
        this.lvLbl_.setLocaleKey("name_toolTable", lv)
        if (lv == cfgHelper.getMaxToolTableLv()) {
            this.btnTableNode_.Child('lb').Swih('max')
        } else {
            this.btnTableNode_.Child('lb').Swih('up')
        }
        // this.btnTableNode_.Child('redDot').active = this.model.checkTableRedDot()
    }

    private refreshHammer() {
        let sk = this.btnMakeNode_.Child('hammer', sp.Skeleton)
        sk.playAnimation('jingzhi', true)
        this.btnMakeNode_.Child('lb').active = true
        this.btnMakeNode_.Component(cc.Button).interactable = true
    }

    private setToolBtn(val: boolean = true) {
        this.equipNode_.children.forEach(it => {
            it.Component(cc.Button).interactable = val
        });
        this.lockPressEvt = !val
    }

    private selectTool(type: number) {
        if (this.selectType == type) return
        this.selectType = type
        this.refreshSelect()
        this.refreshCosts()
        this.refreshHero(this.selectType)
    }

    private refreshSelect() {
        this.equipNode_.children.forEach((node) => {
            let data = node.Data as Tool
            if (data.getType() == this.selectType) {
                node.Child('select').active = true
                node.Child('lv', cc.MultiColor).setColor(data.quality - 1)
                // this.setDescItem(data)
            } else {
                node.Child('select').active = false
                node.Child('lv', cc.MultiColor).setColor(data.quality - 1)
            }
        })
    }

    private refreshCosts() {
        let tool = this.model.getToolByType(this.selectType)
        let cost = tool.cost
        let tableCfg = cfgHelper.getToolTableByLv(this.model.getLv())
        this.btnMakeNode_.Child('lb', cc.Label).setLocaleKey(tool.getLv() > 0 ? 'tool_buttonName_1' : 'tool_buttonName_9')
        this.maxLvNode_.active = tableCfg?.maxToolLv <= this.model.getToolByType(this.selectType).getLv()
        if (this.maxLvNode_.active) {
            this.maxLvNode_.Component(cc.Label).setLocaleKey(this.model.getLv() == cfgHelper.getMaxToolTableLv() ? "tool_guiText_28" : "tool_guiText_27")
        }
        if (!this.maxLvNode_.active && !!cost && cost.length > 0 && !!cost[0]) {
            this.currencyNode_.active = true
            this.titleNode_.active = true
            this.costNode_.active = true
            uiHelper.setCostItems(this.costNode_, cost, this.getTag())
        } else {
            this.currencyNode_.active = false
            this.titleNode_.active = false
            this.costNode_.active = false
        }
    }

    private showComplete(tool: Tool) {
        viewHelper.showPnl("tool/ToolMakeResultPnl", tool)
    }

    private showTips(tool: Tool) {
        let info = this.tipsNode_.Child('info')
        let attr = this.tipsNode_.Child('bg/attr')
        info.Child('name', cc.Label).setLocaleKey(tool.name)
        info.Child('lv', cc.Label).setLocaleKey('tool_guiText_7', tool.getLv())
        attr.Items(TOOL_ATTR_CFG, (node, data, i) => {
            if (!tool[`${data.attr}`]) {
                node.active = false
                return
            }
            let preCfg = tool.getPreCfg()
            let preVal = 0
            if (!preCfg) {
            } else {
                preVal = tool.getPreCfg()[`${data.attr}`] || 0
            }
            node.Child('lb', cc.Label).setLocaleKey(data.lb)
            node.Child("num/val", cc.Label).string = `${preVal}`
            node.Child("num/plus", cc.Label).string = `+${tool[`${data.attr}`] - preVal}`
            node.Child("typeSp", cc.MultiFrame).setFrame(tool.getType() - 1)
        })
        attr.Component(cc.Layout).updateLayout()
        this.tipsNode_.width = Math.max(582, attr.width + 40)

        let root = this.tipsNode_
        let startY = this.tipsPosNode_.y
        root.active = true
        root.opacity = 0
        cc.Tween.stopAllByTarget(root)
        //飘出
        cc.tween(root)
            .to(0.3, { y: startY + 30, opacity: 255 }, { easing: cc.easing.sineOut })
            .delay(1.5)
            .to(0.1, { y: startY + 50, opacity: 0 }, { easing: cc.easing.sineIn })
            .call(() => {
                root.active = false
                root.y = startY
            }).start()
    }

    private playLvUpAnim(type: number) {
        this.equipNode_.children.forEach((node) => {
            if (node.Data instanceof Tool) {
                if (node.Data.getType() == type) {
                    let rand = ['', '2', '3']
                    node.Child('lvUpAnim').active = true
                    let sk = node.Child('lvUpAnim', sp.Skeleton)
                    sk.playAnimation(`animation${rand.random()}`).then(() => {
                        node.Child('lvUpAnim').active = false
                    })
                }
            }
        })
    }

    private async refreshHero(type: number) {
        let hero = this.heroNode_.Child('hero')

        const planet = this.heroNode_.Child('planet')

        resHelper.loadOwnRoleSp(1005, hero, this.getTag())

        let toolNode = hero.Child("tool")
        let upNode = hero.Child("up")

        let tool = this.model.getToolByType(type)
        if (type == PlanetMineType.SEED) {
            resHelper.loadOwnRoleSp(1005, upNode, this.getTag())
            toolNode.active = true
            let sk = toolNode.Component(sp.Skeleton)
            sk.playAnimation("idle", true)
            sk.setSkin(tool.skin)

            upNode.active = true
            hero.Component(sp.Skeleton).playAnimation(HeroAnimation.PICK_SEED_IDLE_DOWN, true)
            upNode.Component(sp.Skeleton).playAnimation(HeroAnimation.PICK_SEED_IDLE_UP, true)
        }
        else {
            if (tool.getLv() > 0) {
                resHelper.switchTool(hero.Component(sp.Skeleton), tool.anim, tool.skin, this.getTag())
            } else {
                resHelper.hideTool(hero.Component(sp.Skeleton))
            }
            toolNode.active = false
            upNode.active = false
            hero.Component(sp.Skeleton).playAnimation(PassengerLifeAnimation.IDLE, true)
        }
 
        let icon = null
        let model = gameHelper.planet.getCurPlanet()
        let nodes = model.getBranchCurMap().getUnCompleteNodes()
        let pModel: PlanetMineModel = null
        for (let node of nodes) {
            if (node instanceof PlanetMineModel) {
                if (node.type == type) {
                    icon = node.icon
                    pModel = node
                    break
                }
            }
        }
        if (!pModel) {
            nodes = model.getBranchCurMap().getNodes()
            for (let i = nodes.length - 1; i > 0; i--) {
                let node = nodes[i]
                if (node instanceof PlanetMineModel) {
                    if (node.type == type) {
                        icon = node.icon
                        pModel = node
                        break
                    }
                }
            }
        }

        if (!pModel) {
            planet.active = false
            return
        }
        planet.active = true

        if (!icon) {
            let cfg = cfgHelper.getMiscData('toolPnlMine')
            let json = assetsMgr.getJsonData<PlanetMineCfg>("PlanetMine", cfg[type - 1]?.id)
            icon = json.icon
        }
        let spf = await assetsMgr.loadTempRes(`planet/mine/${icon}`, cc.SpriteFrame, this.getTag())
        if (!cc.isValid(this)) {
            assetsMgr.releaseTempResByTag(this.getTag())
            return
        }

        planet.Component(cc.Sprite).spriteFrame = spf
        const hpBar = planet.Child("hpBar")
        const infoLayout = planet.Child("infoLayout")
        if (!pModel) {
            hpBar.active = infoLayout.active = false
            return void console.error("没有model数据？？")
        }
        hpBar.active = infoLayout.active = true
        hpBar.y = planet.height + 50
        infoLayout.y = planet.height + 110
        const infoIcon = infoLayout.Child('icon', cc.Sprite)
        const infoTxt = infoLayout.Child('info', cc.Label)
        if (pModel.type == PlanetMineType.TREE) {
            infoIcon.Component(cc.MultiFrame).setFrame(2)
        } else if (pModel.type == PlanetMineType.ORE) {
            infoIcon.Component(cc.MultiFrame).setFrame(1)
        } else if (pModel.type == PlanetMineType.PART) {
            infoIcon.Component(cc.MultiFrame).setFrame(0)
        }

        infoTxt.setLocaleKey("common_guiText_11", assetsMgr.lang(pModel.name) + " " + pModel.lv)
    }

    private initSelectType(id?: number) {
        if (id != null) {
            this.selectType = id
            return
        }
        let type = 1
        let model = gameHelper.planet.getCurPlanet()
        let nodes = model.getBranchCurMap().getUnCompleteNodes()
        for (let node of nodes) {
            if (node instanceof PlanetMineModel) {
                type = node.type
                break
            }
        }
        this.selectType = type
    }

    private setDescItem(tool: Tool) {
        const attrN = this.descNode_.Child("attr")
        const effectL = this.descNode_.Child("effect", cc.Label)

        attrN.Items(TOOL_ATTR_CFG, (node, data, i) => {
            if (!tool[`${data.attr}`]) {
                node.active = false
                return
            }
            if (i != 0) {
                effectL.setLocaleKey(data.effect)
            }
            node.Child('lb', cc.Label).setLocaleKey(data.lb)
            node.Child("val", cc.Label).string = `${tool[`${data.attr}`]}`
            node.Child("typeSp", cc.MultiFrame).setFrame(tool.getType() - 1)
        })
        this.descNode_.active = true
    }

    private updateTitleMax() {
        let tableCfg = cfgHelper.getToolTableByLv(this.model.getLv())
        this.titleLbl_.setLocaleKey("ui_tool_make_max_tip", tableCfg.maxToolLv)
    }

    private getTools() {
        let tools = this.model.getTools()
        let curMap = gameHelper.planet.getCurPlanet().getBranchCurMap()
        if (curMap.getBranch()?.id == "1018-1") {
            tools = tools.filter(it => it.getType() == PlanetMineType.SEED)
        }
        else {
            tools = this.model.getMainTools()
        }
        return tools
    }

}
