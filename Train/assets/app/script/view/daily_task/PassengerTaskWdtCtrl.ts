import { DailyTaskState, DailyTaskType } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { uiHelper } from "../../common/helper/UIHelper";
import ConditionObj from "../../model/common/ConditionObj";
import { DailyTask } from "../../model/daily_task/DailyTaskModel";
import MaskRedCmpt from "../cmpt/common/MaskRedCmpt";
import FrameIconNum from "../prefab/FrameIconNum";

const { ccclass } = cc._decorator;
@ccclass
export default class PassengerTaskWdtCtrl extends mc.BaseWdtCtrl {
    //@autocode property begin
    protected baseItemNode_: cc.Node = null // path://root/base_item_n
    protected itemsSv_: cc.ScrollView = null // path://root/items_sv
    //@end
    public listenEventMaps() {
        return [
            {
                [EventType.DAILY_TASK_FINISH]: this.onDailyTaskFinish
            }
        ]
    }

    public async onCreate() {
        gameHelper.dailyTask.pnlOpenRed = true
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    public init(rootNode: cc.Node) {
        this.updateBigReward()
        this.updateSv()
    }

    async onDailyTaskFinish(task: DailyTask, anim: boolean = true) {
        this.updateBigReward()
        const it = this.itemsSv_.content.children.find(it => it.Data == task)
        if (!it) { return }
        if (!anim) {
            return void this.updateOne(it, task)
        }
        await cc.tween(it).to(.3, { scale: 0 }).start().promise()
        this.itemsSv_.content.removeChild(it, false)
        this.itemsSv_.content.Component(cc.Layout).updateLayout()
        this.itemsSv_.content.addChild(it)
        this.updateOne(it, task)
        it.scale = 1
    }

    updateBigReward() {
        const it = this.baseItemNode_
        it.Child("0").active = true
        const rewardsNode = it.Child("rewards")
        const r = cfgHelper.getMiscData("dailyTask").bigReward
        const rewards = gameHelper.toConditions(r)
        this.setReward(rewards, rewardsNode)
        const tasks = gameHelper.dailyTask.getTasks()
        const count = tasks.reduce((pre, cur) => {
            if (cur.state == DailyTaskState.FINISH) {
                return pre + 1
            }
            return pre
        }, 0)
        it.Child("0/progress/lbl", cc.Label).string = `${count}/${tasks.length}`
        it.Child("0/progress/mask").width = it.Child("0/progress/mask/bg").width * count / tasks.length
        this.updateState(it.Child("state"), true)
    }

    updateState(stateNode: cc.Node, big: boolean) {
        let isFinish = false
        let canGet = false
        let data: DailyTask = null
        if (big) {
            isFinish = gameHelper.dailyTask.bigGet
            canGet = gameHelper.dailyTask.canGetBit()
        }
        else {
            data = stateNode.Data as DailyTask
            isFinish = data.state == DailyTaskState.FINISH
            canGet = data.canFinish()
        }
        stateNode.Swih(isFinish ? 2 : 1)
        const submitBtn = stateNode.Child("1/get")
        submitBtn.off('click')
        submitBtn.on('click', () => {
            gameHelper.dailyTask.finishTask(data)
        })
        submitBtn.Component(MaskRedCmpt).init({ task: data, big })
        submitBtn.Component(cc.MultiFrame).setFrame(canGet)
        
        if (!isFinish) {
            if (big) {
                submitBtn.Component(cc.Button).interactable = canGet
            }
        }
    }

    updateSv() {
        const tasks = gameHelper.dailyTask.getTasks()
        const sorted = tasks.slice().sort((a, b) => {
            if (a.canFinish()) return -1
            if (b.canFinish()) return 1
            return a.state - b.state
        })
        this.itemsSv_.Items(sorted, (it, task) => this.updateOne(it, task))
    }

    updateOne(it: cc.Node, task: DailyTask) {
        it.Data = task
        const rewardsNode = it.Child("rewards")
        const state = it.Child("state")
        state.Data = task
        this.updateState(state, false)
        it = it.Child("1")
        this.setPublisher(task, it.Child("icon"))
        const taskDescLabel = it.Child("lbl"), taskDescLabel2 = it.Child("lbl_2")
        const req = it.Child("req")
        const monsters = it.Child("monsters")
        const dialog = it.Child("dialog")

        const planet = gameHelper.planet.getPlanet(task.getPlanet())
        dialog.Child("lbl").setLocaleKey(planet.name)
        
        switch (task.type) {
            case DailyTaskType.COLLECT:
            case DailyTaskType.EQUIP:
                req.active = true
                monsters.active = false
                taskDescLabel2.active = false
                taskDescLabel.setLocaleKey(task.content)
                req.Items(task.getTarget(), (it, data) => {
                    const icon = it.Component(FrameIconNum)
                    icon.init(data, this.getTag())
                    const isFinish = task.state == DailyTaskState.FINISH
                    if (isFinish) {
                        icon.setDone()
                    }
                    else {
                        icon.setAsCostCheck("#2fac1d", "#f91f1e", "#ffffff", 4, 48)
                        uiHelper.regClickPropBubble(it, data)
                    }
                    // let icon = it.Child('icon')
                    // icon.scale = task.type == DailyTaskType.EQUIP ? 0.5 : 0.8
                    // resHelper.loadIconByCondInfo(data, icon, this.getTag())
                    // let cur = gameHelper.getNumByCondition(data)

                    // it.Child('num').active = !isFinish
                    // it.Child('gou').active = isFinish
                    // if (!isFinish) {
                    //     it.Child('num', cc.Label).string = `${cur}/${data.num}`
                    //     it.Child('num', cc.MultiColor).setColor(cur < data.num)

                    // }
                })
                break
            case DailyTaskType.DIALOG:
                req.active = false
                monsters.active = false
                taskDescLabel2.active = true
                const arg = task.dialogArg
                taskDescLabel.setLocaleKey(task.content, assetsMgr.lang(arg.planet.name), assetsMgr.lang(arg.dialogNpc.name))
                taskDescLabel2.Component(cc.Label).string = `"${assetsMgr.lang(arg.dialog.lang)}"`
                break
            case DailyTaskType.BATTLE:
                req.active = false
                monsters.active = true
                taskDescLabel2.active = false
                taskDescLabel.setLocaleKey("passengerTask_N_desc_4_1")

                monsters.Items(task.battleInfo, (it, data) => {
                    resHelper.loadMonsterCircleIcon(data.id, it.Child("icon"), this.getTag())
                    it.Child("num", cc.Label).setLocaleKey('common_guiText_11', data.lv)
                })
                break
        }
        this.setReward(task, rewardsNode)
    }

    setReward(task: DailyTask | ConditionObj[], node: cc.Node) {
        let list = task instanceof DailyTask ? task.getRewards() : task
        node.Items(list, (it, data) => {
            it.Component(FrameIconNum).init(data, this.getTag())
            it.Component(FrameIconNum).setNumLabel("#5f4a2c", "#ffffff", 4, 48)
            uiHelper.regClickPropBubble(it, data)
        })
    }

    setPublisher(task: DailyTask, node: cc.Node) {
        const sender = task.getSender()
        resHelper.loadRoleBigIcon(sender, node.Component(cc.Sprite), this.getTag())
    }
}
