import { util } from "../../../core/utils/Utils";
import { Msg } from "../../../proto/msg-define";
import { PlanetCfg, TransportLevelCfg } from "../../common/constant/DataType";
import { BattleLevelType, MarkNewType, NPC_ID, UIFunctionType } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import Monster from "../battle/Monster";
import ConditionObj from "../common/ConditionObj";

const { ccclass, property } = cc._decorator;


export class Transport {
    private starLv: number = 0
    private start: number = 0
    private end: number = 0
    private rewards: ConditionObj[] = []
    private fixRewards: ConditionObj[] = []
    private state: proto.TransportDataState = 0
    private cfg: TransportLevelCfg = null
    private load: number = 0
    private rare: boolean = false
    private actor: number = -1
    private langKey: string = ""
    private uniqueId: string = ""
    private timeStoneKey: boolean = false

    public battleData: proto.ITransportBattleData

    public init(data: proto.ITransportData) {
        this.starLv = data.starLv
        this.start = data.start
        this.end = data.end
        this.rewards = gameHelper.toConditions(data.rewards)
        this.fixRewards = gameHelper.toConditions(data.fixRewards)
        this.state = data.state
        this.load = data.load
        this.rare = data.rare
        this.timeStoneKey = data.timeStoneKey
        this.actor = data.actor
        this.langKey = data.key
        this.battleData = data.battleData
        this.cfg = assetsMgr.getJsonData<TransportLevelCfg>('TransportLevel', this.starLv)
        this.uniqueId = "" + this.end
        return this
    }

    public getLangKey() { return this.langKey }
    public getActor() { return this.actor }
    public isRare() { return this.rare }
    public isTimeStoneKey() { return this.timeStoneKey }
    public getStarLv() { return this.starLv }
    public getName() {
        return assetsMgr.getJsonData<PlanetCfg>("Planet", this.end)?.name
    }
    public getRewards() { return this.rewards.concat(this.fixRewards) }
    public getState() { return this.state }
    public getTarget() { return this.end }
    public getLoad() { return this.load }
    public getTaskId() { return this.uniqueId }

    public get monsterLv() { return this.cfg?.monster[0].lv }

    public take() {
        this.setState(proto.TransportDataState.Pull)
    }

    public isTake() {
        return this.state == proto.TransportDataState.Pull
    }

    public setState(state) {
        if (this.state == state) return
        this.state = state
        // 领奖的时候再标记成完成?
        if (state == proto.TransportDataState.Over) {
            eventCenter.emit(EventType.TRANSPORT_FINISH, this)
        }
        if (state == proto.TransportDataState.Failed) {
            eventCenter.emit(EventType.TRANSPORT_FAILED, this)
        }
        eventCenter.emit(EventType.TRANSPORT_STATE_CHANGE, this)
    }
}



@mc.addmodel('transport')
export default class TransportModel extends mc.BaseModel {

    public data: proto.ITransport = null
    private transportList: Transport[] = []
    private exp: number = 0 // 运送经验值

    // 当前进行中的任务
    private current: Transport = null

    // 运送等级
    get level(): number {
        return cfgHelper.calculateTransportLevel(this.exp)
    }

    public getExp() { return this.exp }
    public getTransportList() { return this.transportList }
    public getTransportViewList() {
        if (!this.transportList || this.transportList.length == 0) return this.transportList
        let isDone = t => t.state == proto.TransportDataState.Done || t.state == proto.TransportDataState.Over
        let list1 = this.transportList.filter(isDone), list2 = this.transportList.filter(t => !isDone(t))
        return list2.concat(list1);
    }
    public getCurTransport() { return this.current }

    public init() {
        this.updateInfo(this.data)

        this.initListener()
    }

    private initListener() {
        eventCenter.on(EventType.UNLOCK_FUNTION, (type) => {
            if (type == UIFunctionType.PLAY_TRANSPORT) {
                this.sync()
            }
        })
    }


    public getLevelUpInfo() {
        const lv = this.level

        let exp = this.exp
        let need = 0
        const ary = assetsMgr.getJson<TransportLevelCfg>('TransportLevel').datas
        for (const item of ary) {
            if (item.id <= lv) {
                exp -= item.exp
            }
            if (item.id == lv + 1) {
                need = item.exp
            }
        }
        return { exp, need }
    }

    public updateInfo(data: proto.ITransport) {
        if (data?.list) {
            this.transportList = data?.list.map(t => new Transport().init(t))
        }
        this.exp = data.exp

        const moveTargetId = gameHelper.planet.moveTargetId
        if (moveTargetId) {
            this.updateCurrentTransportOnPlanetMove(moveTargetId)
        }
    }

    public isInit() {
        return this.transportList.length > 0
    }

    public checkSync() {
        if (!this.isInit()) {
            return this.sync()
        }
        return true
    }

    public async sync() {
        const { code, transport } = await gameHelper.net.requestWithData(Msg.C2S_SyncTransportMessage)
        if (code == 0) {
            this.updateInfo(transport)
            return true
        }
        return false
    }

    public getTransportByIndex(index: number) {
        try {
            return this.transportList[index]
        } catch (e) {
            return null
        }
    }

    // 获取当前已经使用的载重
    public getLoad() {
        return this.transportList.reduce((sum, t) => {
            if (t.getState() == proto.TransportDataState.Pull) {
                sum += t.getLoad()
            }
            return sum
        }, 0)
    }

    /**
     * 当前负重检查
     * @param add 
     * @returns 
     */
    public checkLoadIsValid(add: number): boolean {
        const max = gameHelper.train.getLoad()
        const sum = this.getLoad()
        return sum + add <= max
    }

    //接任务
    public async takeTransport(index: number) {
        const transport = this.getTransportByIndex(index)
        if (!this.checkLoadIsValid(transport.getLoad())) {
            return viewHelper.showAlert("tip_transport_load_not_enough")
        }
        return await new Promise((resolve, reject) => {
            viewHelper.showMessageBox("tip_transport_pull", async () => {
                let msg = new proto.C2S_TransportStartMessage({ index })
                let res = await gameHelper.net.request(Msg.C2S_TransportStartMessage, msg, true)
                const { code } = proto.S2C_TransportStartMessage.decode(res)
                if (code) {
                    viewHelper.showNetError(code)
                    resolve(false)
                    return
                }
                transport.take()
                this.current = transport
                eventCenter.emit(EventType.TRANSPORT_STATE_CHANGE, transport)
                eventCenter.emit(EventType.TRANSPORT_TAKE, transport)
                resolve(true)
            }, null, { title: "common_guiText_23" })
        })
    }

    // 领取奖励
    public async getTransportReward(index: number = -1) {
        let transport: Transport = null
        if (index == -1) {
            const ary = this.getTransportByState(proto.TransportDataState.Done)
            transport = ary[0]
            index = this.transportList.indexOf(transport)
        } else if (this.current) {
            transport = this.current
        } else {
            transport = this.getTransportByIndex(index)
        }
        if (!transport) {
            return void viewHelper.showNetError(666)
        }

        let msg = new proto.C2S_TransportRewardGetMessage({ index })
        let res = await gameHelper.net.request(Msg.C2S_TransportRewardGetMessage, msg, true)
        const { code } = proto.S2C_TransportRewardGetMessage.decode(res)
        if (code) {
            return viewHelper.showNetError(code)
        }

        gameHelper.grantRewardAndShowUI(transport.getRewards())

        transport.setState(proto.TransportDataState.Over)

        if (transport.isRare()) {
            this.exp += cfgHelper.getMiscData("transport").rareExp
        }
        this.current = null
        eventCenter.emit(EventType.TRANSPORT_STATE_CHANGE, transport)
    }

    @util.addLock
    public async battle() {
        eventCenter.emit(EventType.ENERGY_STOP_AUTO)

        const currentTransport = this.getCurTransport()
        let monsters = currentTransport.battleData.monsters.map(({ id, lv, starLv }) => {
            return new Monster().init(id, lv, starLv)
        })
        let {exp} = gameHelper.transport.getLevelUpInfo()
        let level = gameHelper.transport.level

        let res = await viewHelper.showBattle({ 
            monsters, battleBg: resHelper.getBattleBg("transport"), rewards: null, noAgain: true, isTransport: true,
            levelType: BattleLevelType.TRANSPORT, levelId: `${level}-${Math.floor(exp / 10) + 1}`,
        }, false)
        if (!!res) {
            await this.transportFight(res?.isWin)
        }
    }

    public async transportFight(isWin: boolean) {
        let msg = new proto.C2S_TransportFightMessage({ planetId: this.getCurTransport().getTarget(), succ: isWin })
        let res = await gameHelper.net.request(Msg.C2S_TransportFightMessage, msg, true)
        const { code } = proto.S2C_TransportFightMessage.decode(res)
        if (code) {
            viewHelper.showNetError(code)
            return false
        }

        this.updateMoveBattle(isWin)
    }

    public updateMoveBattle(isWin: boolean) {
        let failTip = isWin ? null : 'transport_guiText_13'
        viewHelper.showPnl('battle/BattleResult', {
            isWin, failTip, isTransport: true, noAgain: true, callback: () => {
                let resetCallback = null
                if (!isWin) {
                    this.getCurTransport().setState(proto.TransportDataState.Failed)
                    resetCallback = () => {
                        this.current = null
                    }
                } else {
                    resetCallback = () => {
                        this.getCurTransport().battleData = null
                    }
                }
                // 海盗动画完了 再恢复航行状态
                eventCenter.emit(isWin ? EventType.TRANSPORT_DEFEAT_MONSTER : EventType.TRANSPORT_UN_DEFEAT_MONSTER, resetCallback)
            }
        })

        // viewHelper.closePnl("battle/BattlePnl")
        // eventCenter.emit(isWin ? EventType.TRANSPORT_DEFEAT_MONSTER : EventType.TRANSPORT_UN_DEFEAT_MONSTER)
    }

    public getCurBattle() {
        const data = this.getCurTransport()
        return data?.battleData
    }

    public async transportBack() {
        let msg = new proto.C2S_TransportBackMessage()
        let res = await gameHelper.net.request(Msg.C2S_TransportBackMessage, msg, true)
        const { code } = proto.S2C_TransportBackMessage.decode(res)
        if (code != 0) {
            viewHelper.showNetError(code)
            return false
        }
        this.onFail()
        let id = 1007
        gameHelper.planet.reach(id)
        viewHelper.gotoPlanetEntry()
        return true
    }

    private onFail() {

    }

    /**
     * 航行移动时 更新当前进行中的任务
     * @param planetId 
     */
    public updateCurrentTransportOnPlanetMove(planetId: number) {
        let ary = this.getTransportByState(proto.TransportDataState.Pull)
        if (ary.length) {
            ary = ary.filter(t => t.getTarget() == planetId)
            this.current = ary[0]
        }
    }

    /**
     * 获取指定状态的运送任务列表
     * @param state 
     * @param not 结果取反 
     * @returns 
     */
    public getTransportByState(state: proto.TransportDataState, not: boolean = false): Transport[] {
        if (!this.transportList) return []
        return this.transportList.filter(t => not ? t.getState() != state : t.getState() == state)
    }

    /**
     * 获取所有已经接取的运送的目的地
     * @returns 
     */
    public getDestinationAry(): number[] {
        const ary = this.getTransportByState(proto.TransportDataState.Pull)
        return ary.map(t => t.getTarget())
    }

    /**
     * 获取可以领奖的运送任务
     */
    public getCanDoneTransport(): Transport | null {
        const ary = this.getTransportByState(proto.TransportDataState.Done)
        return ary.length ? ary[0] : null
    }

    /**
     * 遍历所有未完成的任务 看看是否可以完成
     * @param planetId 星球id
     */
    public tryDoneTransportByPlanetId(planetId: number): boolean {
        const ary = this.getTransportByState(proto.TransportDataState.Pull)
        let done = false
        for (const transport of ary) {
            if (transport.getTarget() == planetId) {
                transport.setState(proto.TransportDataState.Done)
                done = true
            }
        }
        return done
    }

    public getPullTransport() {
        return this.getTransportByState(proto.TransportDataState.Pull)
    }

    /**
     * 是否有运送中的任务
     * @returns 
     */
    public isTransporting() {
        return this.getPullTransport().length != 0
    }

    /**
     * 是否有完成的任务
     * @returns 
     */
    public isTransportDone() {
        return !!this.getTransportByState(proto.TransportDataState.Done).length
    }

}
