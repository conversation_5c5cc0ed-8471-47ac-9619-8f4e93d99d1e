import { Msg } from "../../../proto/msg-define";
import { ConditionType, EnergyRecoverType, ItemID } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { cfgHelper } from "../../common/helper/CfgHelper"
import { gameHelper } from "../../common/helper/GameHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import ConditionObj from "../common/ConditionObj";

export default class EnergyModel {
    public hasSpeedUp: boolean = false

    private cfg: any = null

    private energy: number = 0 //游戏实时值，本质是毫秒
    public freeRecoverNum: number = 0 //免费剩余恢复次数
    public isSpeedUp: boolean = false
    public reqSpeedUp: number = 0
    public recoverEndTime: number = -1

    get recoverSpeed() { return this.cfg?.recoverSpeed || 0 } //x秒回1点体力
    get max() {
        let base = 0
        if (this.cfg) {
            base = this.cfg.max
        }
        return base * ut.Time.Second
    }

    public init(data: any = {}) {
        this.cfg = cfgHelper.getMiscData('speedUp')
        this.updateInfo(data)
        return this
    }

    public updateInfo(data: proto.IEnergy) {
        this.energy = data.energy
        this.freeRecoverNum = data.freeRecoverNum
        this.hasSpeedUp = data.used
        this.isSpeedUp = data.isSpeedUp
        this.recoverEndTime = data.nextRecoverTime + gameHelper.now()
    }

    public addEnergy(val: number) {
        this.energy = cc.misc.clampf(0, this.max, this.energy + val)
    }

    public setEnergy(val: number) {
        this.energy = val
    }

    public getEnergy() {
        return this.energy
    }

    public getTotalRecoverCnt() {
        return this.cfg.recoverFree + this.cfg.recoverBuy
    }

    @ut.addLock
    public async recoverEnergy(type: EnergyRecoverType) {
        const { code, energy } = await gameHelper.net.requestWithData(Msg.C2S_RecoverEnergyMessage, { type })
        if (code != 0) {
            return void viewHelper.showNetError(code)
        }
        this.updateInfo(energy)
        let cond = null
        switch (type) {
            case EnergyRecoverType.ENERGY:
                cond = new ConditionObj().init(ConditionType.PROP, ItemID.ENERGY, 1)
                break
            case EnergyRecoverType.AD:
                cond = new ConditionObj().init(ConditionType.AD, proto.AdType.RecoveryTrainEnergy, 1)
                break
        }
        if (cond) {
            gameHelper.deductCondition(cond)
        }
        return true
    }

    update(dt) {
        if (gameHelper.world.isSpeedUp()) {
            this.addEnergy(-dt * ut.Time.Second)
        }
        if (this.getRecoverTime() <= 0) {
            this.syncFreeCnt()
        }
    }

    public checkRedDot() {
        let res: boolean = false
        res = this.energy <= 0 && this.freeRecoverNum > 0
        return res
    }

    // 解锁自动加速
    public unlockAuto() {
        this.addEnergy((this.cfg.autoMax - this.cfg.max) * ut.Time.Second)
        eventCenter.emit(EventType.ENERGY_UNLOCK_AUTO)
    }

    public getRecoverTime() { return Math.max(0, this.recoverEndTime - gameHelper.now()) }

    @ut.addLock
    public async syncFreeCnt() {
        if (this.getRecoverTime() > 0) return
        const r = await gameHelper.net.requestWithData(Msg.C2S_SyncEnergyMessage)
        if (!r || !r.energy) return
        this.updateInfo(r.energy)
        eventCenter.emit(EventType.ENERGY_SYNC)
    }

}
