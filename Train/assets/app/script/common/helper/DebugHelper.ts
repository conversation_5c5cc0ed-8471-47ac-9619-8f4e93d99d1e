import { Msg } from "../../../proto/msg-define";
import ConditionObj from "../../model/common/ConditionObj";
import ActionTree from "../../model/passenger/ActionTree";
import EventType from "../event/EventType";
import { ChapterPlanetMonsterCfg, CharacterCfg, CharacterProfileCfg, EquipCfg, ItemCfg, PlanetCfg, TrainCfg, WantedCfg } from "../constant/DataType";
import { CarriageID, ConditionType, DailyTaskState, ItemID, ItemType, OreLandType, TrainBurstTaskType } from "../constant/Enums";
import { gameHelper } from "../helper/GameHelper";
import { viewHelper } from "../helper/ViewHelper";
import { dbHelper } from "./DatabaseHelper";
import { dropItemHelper } from "./DropItemHelper";
import { resHelper } from "./ResHelper";
import { cfgHelper } from "./CfgHelper";
import BattleSkill from "../../model/battle/BattleSkill";
import Tool from "../../model/tool/Tool";
import CollectWindCtrl from "../../view/collect/CollectWindCtrl";

if (CC_DEV) {
    class DebugHelper {
        private revFlag = false

        public listen() {

        }

        public onGm(command: string) {
            if (!command) return
            const splits = command.split(" ")
            const params = splits.length > 1 ? splits[1].split(",") : []
            switch (splits[0]) {
                case "@add-currency":
                    return void this.addCurrency(Number(params[0]), Number(params[1]))
                case "@add-item":
                    return void this.addProp(Number(params[0]), Number(params[1]))
                case "@set-energy":
                    return void this.setEngery(Number(params[0]))
                case "@clear-train-build-cd":
                    return void this.trainBuilt()
                case "@train-item-built":
                    return void this.trainItemBuilt(Number(params[0]), Number(params[1]))
                case "@add-passenger":
                    return void this.addPassenger(Number(params[0]))
                case "@add-worldtime":
                    return void this.addWorldTime(Number(params[0]))
                case "@set-planet":
                    return void this.setPlanetProgress(Number(params[0]), Number(params[1]), Number(params[2]))
                case "@auto-explore":
                    return void this.setAutoExplore(Boolean(params[0]))
                case "@unlock-build":
                    return void this.unlockBuild(params[0])
                case "@complete-task":
                    return void this.completeTask(params[0])
                case "@all-passenger":
                    return void this.unlockAllPassengers()
                case "@set-passenger-lv":
                    return this.setPassengerLv(params[0], params[1])
                case "@set-passenger-starlv":
                    return this.setPassengerStarLv(params[0], params[1])
                case "@add":
                    return this.add(params[0], params[1], params[2])
            }
        }

        public sendGm(cmd: string, ...args) {
            if (this.revFlag) {
                return
            }
            let msg = new proto.C2S_GmExecuteMessage()
            msg.cmd = "@" + cmd
            if (args && args.length) {
                msg.cmd += " " + args.join(",")
            }
            return gameHelper.net.requestWithMsg(Msg.C2S_GmExecuteMessage, msg)
        }


        public clearData() {
            dbHelper.clear()
            gameHelper.gameRestart()
        }

        public addStarDust(val: number, isEmit: boolean = true) {
            this.addCurrency(ConditionType.STAR_DUST, val)
        }

        public addHeart(val, isEmit: boolean = true) {
            this.addCurrency(ConditionType.HEART, val)
        }

        public addDiamond(val: number, isEmit: boolean = true) {
            this.addCurrency(ConditionType.DIAMOND, val)
        }

        public async addCurrency(type, val) {
            await this.sendGm("add-currency", type, val)
            gameHelper.currency.changeCurrency(type, val)
        }


        public async addProp(id, count) {
            let cfg = cfgHelper.getPropData(id)
            if (!cfg) {
                return
            }
            if (cfg.type == ItemType.CHEST) {
                let cfg2 = cfgHelper.getChestData(id)
                if (cfg2) {
                    this.addChest(cfg2.id, count)
                }
                return
            }
            await this.sendGm("add-item", id, count)
            if (cfg.isUnique) {
                let info = await gameHelper.user.downloadRecord()
                gameHelper.bag.data = info.player.bag
                gameHelper.bag.init()
            }
            else {
                gameHelper.bag.changeProp(id, count)
            }
        }

        public async setEngery(val) {
            await this.sendGm("set-energy", val)
            gameHelper.world.energy.setEnergy(val)
        }


        public async watchStore(cnt: number = 100, id: number = 1) {
            const binary = await this.sendGm("watch-store", id, cnt)
            const s2CGmExecuteRspMessage = proto.S2C_GmExecuteRspMessage.decode(binary);
            if (!s2CGmExecuteRspMessage.reply) return
            const pl: any = JSON.parse(s2CGmExecuteRspMessage.reply)
            let out = `刷新次数\t栏位序号\t商品type\t商品id\t商品num\t商品折扣\t商品总价type\t商品总价num\t\n`
            let time = 0
            for (let ele of pl) {
                time++
                let index = 0
                for (let it of ele.goods) {
                    out += `${time}\t${index}\t${it.item.type}\t${it.item.id}\t${it.item.num}\t${it.discount}\t${it.cost.type}\t${it.cost.num}\t\n`
                    index++
                }
            }
            console.log(out)
        }

        public async watchJackpot(cnt: number) {
            await this.addCurrency(1, cnt * 300)
            const { reply } = await this.sendGm("jackpot", cnt)
            //console.log(s2CGmExecuteRspMessage.reply)
            const idMap = JSON.parse(reply)
            console.log(idMap)
            let qMap = {}
            for (let id in idMap) {
                let data = cfgHelper.getCharacter(Number(id))
                if (!qMap[data.quality]) qMap[data.quality] = 0
                qMap[data.quality] += idMap[id]
            }
            console.log(qMap)
        }

        public async watchInstance(id: number, lv: number, cnt: number) {
            const binary = await this.sendGm("watch-instance", id, lv, cnt)
            const s2CGmExecuteRspMessage = proto.S2C_GmExecuteRspMessage.decode(binary);
            //console.log(s2CGmExecuteRspMessage.reply)
            const pl = JSON.parse(s2CGmExecuteRspMessage.reply)
            console.log(pl)
            let out = `开采次数\t装备id\t装备lv\t\n`
            for (let i = 0; i < pl.length; i++) {
                let ele = pl[i]
                out += (`${i}\t${ele.Id}\t${ele.Level}\n`)
            }
            console.log(out)
        }

        public async watchEntrust(cnt: number) {
            cnt = Math.max(0, cnt)
            const binary = await this.sendGm("watch-entrust", cnt)
            const s2CGmExecuteRspMessage = proto.S2C_GmExecuteRspMessage.decode(binary);
            if (!s2CGmExecuteRspMessage.reply) return
            const pl: any = JSON.parse(s2CGmExecuteRspMessage.reply)
            console.log()
            let a = 0
                , b = 0
                , out = `类型 \t配置id \t星级 \t委托人 \t背景图 \t怪物等级 \t奖励 \t\n`
                , obj = {}
            for (let ele of pl) {
                let rewardStr = ''
                let i = 0
                ele.reward?.forEach(val => {
                    rewardStr += `奖励[${i}].type:${val.type} 奖励[${i}].id:${val.id} 奖励[${i}].num:${val.num} \t`
                    i++
                })
                out += (`${ele.type == 1 ? "生活" : "战斗"} \t${ele.cfgId} \t${ele.star} \t${ele.principal} \t${ele.bg} \t${ele.monsterLv || []} \t ${rewardStr} \t\n`)
                ele.type == 1 ? a += 1 : b += 1
                obj[ele.cfgId] = obj[ele.cfgId] || 0
                obj[ele.cfgId] += 1
            }
            console.log(out)
            console.log(`总共${cnt}次，生活类型:${a}次，战斗类型:${b}次`)
        }

        public async setTool(lv: number, type?: number) {
            await this.sendGm("up-tool", lv, type)
            if (type != -1) {
                let tool = gameHelper.tool.getToolByType(type)
                if (!tool) {
                    tool = new Tool().init({ type: type })
                    gameHelper.tool.getTools().push(tool)
                }
                tool.init({ type, lv })
            } else {
                let tools = gameHelper.tool.getTools()
                tools.forEach(tool => {
                    tool.init({ type, lv })
                })
            }
        }

        public async watchTool(cnt: number) {
            cnt = Math.max(0, cnt)
            const binary = await this.sendGm("watch-tool", cnt)
            const s2CGmExecuteRspMessage = proto.S2C_GmExecuteRspMessage.decode(binary);
            if (!s2CGmExecuteRspMessage.reply) return
            const pl: any = JSON.parse(s2CGmExecuteRspMessage.reply)
            console.log()
            let a = 0
                , b = 0
                , out = `工具类型\t工具id\t前缀\t品质\t等级\t攻击力\t攻速\t秒伤浮动\t熟练\t价格\t\n`
                , obj = {}
            for (let ele of pl) {
                out += (`${ele.type}\t${ele.id}\t${ele.quality}\t${ele.lv}\t${ele.atk}\t${ele.spd}\t${ele.rank}\t${ele.price}\t\n`)
                ele.type == 1 ? a += 1 : b += 1
                obj[ele.cfgId] = obj[ele.cfgId] || 0
                obj[ele.cfgId] += 1
            }
            console.log(out)
            console.log(`总共${cnt}次`)
        }

        public async watchChest(id: number, cnt: number) {
            const binary = await this.sendGm("test-chest", id, cnt)
            const s2CGmExecuteRspMessage = proto.S2C_GmExecuteRspMessage.decode(binary);
            if (!s2CGmExecuteRspMessage.reply) return
            const pl: any = JSON.parse(s2CGmExecuteRspMessage.reply)
            console.log()
            let out //= `类型 \t配置id \t星级 \t委托人 \t背景图 \t怪物等级 \t奖励 \t\n`
            for (let i in pl) {
                let rewardStr = ''
                let ele = pl[i]
                for (let j in ele) {
                    let val = ele[j]
                    rewardStr += `奖励[${j}].type:${val.Type} 奖励[${j}].id:${val.Id} 奖励[${j}].num:${val.Num} \t`
                }
                out += (`${rewardStr} \t\n`)
            }
            console.log(out)
        }

        public async watchBlackHoleAid(cnt: number = 100) {
            await this.watchGm("watch-blackhole-aid", cnt)
        }

        private async watchGm(cmd, ...params) {
            const binary = await this.sendGm(cmd, ...params)
            const s2CGmExecuteRspMessage = proto.S2C_GmExecuteRspMessage.decode(binary);
            if (!s2CGmExecuteRspMessage.reply) return
            const pl: any = JSON.parse(s2CGmExecuteRspMessage.reply)
            console.log(pl)
            return pl
        }

        public async addChest(id, cnt) {
            await this.sendGm("add-chest", id, cnt)
            let itemId = cfgHelper.getChestItemId(id)
            gameHelper.chest.changeChest(itemId, cnt)
        }

        public async trainBuilt() {
            await this.sendGm("clear-train-build-cd")
            gameHelper.train.getCarriages().forEach(async (carriage) => {
                if (carriage.buildTime > 0) {
                    carriage.updateBuildSurplusTime(0.1)
                }
            })
        }

        public async refreshTransport(lv = 0) {
            await this.sendGm('refresh-transport', lv)
            let info = await gameHelper.user.downloadRecord()
            gameHelper.transport.updateInfo(info.player.transport)
        }

        public async trainItemBuilt(trainId?: number, level?: number) {
            await this.sendGm("train-item-built", trainId || 0, level || 0)
        }

        public all() {
            this.addAllMaterials(100)
            this.addStarDust(1000000)
            this.addHeart(1000000)
            this.addDiamond(10000)
            this.addProp(ItemID.ELECTRIC, 1000000)
            this.addProp(ItemID.WATER, 1000000)
        }

        public addAllMaterials(count: number) {
            this.addProp(ItemID.ELECTRIC, count)
            this.addProp(ItemID.WATER, count)
            this.addPropByType(1, count)
            //this.addPropByType(2, count)
            //this.addPropByType(3, count)
        }

        public addGift(count) {
            this.addPropByType(4, count)
        }

        public addPropByType(type, count) {
            let datas = assetsMgr.getJson<ItemCfg>("Item").datas
            for (let prop of datas) {
                if (prop.type == type) {
                    this.addProp(prop.id, count)
                }
            }
        }

        public async unlockAllPassengers() {
            await this.sendGm("all-passenger")
            let datas = assetsMgr.getJson<CharacterCfg>("Character").datas
            for (let data of datas) {
                if (!data.notOpen) {
                    this.addPassenger(data.id)
                }
            }
        }

        public async addPassenger(id) {
            await this.sendGm("add-passenger", id)
            gameHelper.passenger.addPassenger(id)
        }

        public async setPassengerLv(id, lv) {
            await this.sendGm("set-passenger-lv", id, lv)
            let passengers = id == 0 ? gameHelper.passenger.getPassengers() : [gameHelper.passenger.getPassenger(id)]
            passengers.forEach(passenger => {
                passenger["level"] = lv
            })
        }

        public async setPassengerStarLv(id, lv) {
            await this.sendGm("set-passenger-starlv", id, lv)
            let passengers = id == 0 ? gameHelper.passenger.getPassengers() : [gameHelper.passenger.getPassenger(id)]
            passengers.forEach(passenger => {
                passenger["starLevel"] = lv
            })
        }

        public unlockAllPlanets() {
            let datas = assetsMgr.getJson<PlanetCfg>("Planet").datas
            for (let data of datas) {
                gameHelper.planet.unlockPlanet(data.id)
                this.setPlanetDone(data.id)
            }
        }

        public finishAllGuide() {
            let guides = gameHelper.guide.getGuides()
            for (let guide of guides) {
                guide.finish()
            }
        }

        public async testId10() {
            let id = 10
            await this.unlockGuide(id)
            gameHelper.guide.getGuideInfo(id).progress = 15
            gameHelper.planet.setCurPlanet(1001)
            let planet = gameHelper.planet.getCurPlanet()
            planet.getMap(1).setProgress(25)
            await ut.wait(0.5)
            gameHelper.gameRestart()
        }

        public async testId11() {
            this.setSchoolDone()
            await this.unlockGuide(11)
            await ut.wait(0.5)
            gameHelper.gameRestart()
        }

        public async testId13() {
            this.setSchoolDone()
            await this.unlockGuide(13)
            this.addAllMaterials(10)
        }

        public setSchoolDone() {
            let planet = gameHelper.planet.getSchoolPlanet()
            planet.getMap(1).setDone()
            planet.getMap(2).setDone()
            gameHelper.planet.unlockByLockId(planet.getId())
        }

        public async testId14() {
            await this.unlockGuide(14)
            this.addAllMaterials(100)
            this.addStarDust(10000)
            this.setSchoolDone()
            gameHelper.planet.setCurPlanet(1005)
            let planet = gameHelper.planet.getCurPlanet()
            let map = planet.getMap(1)
            map.setProgress(0)
            map.showLandAnim = false
            gameHelper.task.test14()
            await ut.wait(0.5)
            gameHelper.gameRestart()
        }

        public async testId16(guideId: number = 16, progress: number = 14) {
            await this.unlockGuide(guideId)
            this.addAllMaterials(100)
            this.addStarDust(10000)
            this.setSchoolDone()
            gameHelper.planet.setCurPlanet(1005)
            let planet = gameHelper.planet.getCurPlanet()
            let map = planet.getMap(1)
            map.setProgress(progress)
            map.showLandAnim = true
            await ut.wait(0.5)
            gameHelper.gameRestart()
        }

        public async testId17() {
            this.testId16(17, 17)
        }

        public async testId19() {
            this.testId16(19, 22)
        }

        public async testId21() {
            this.testId16(21, 24)
        }

        public async testId22() {
            this.testId16(22, 24)
        }

        public battleTest() {
            viewHelper.showPnl("battle/BattlePnl", { debug: true, battleBg: resHelper.getPlanetBattleBg(1001) })
        }

        public async addWorldTime(h, m = 0, s = 0) {
            let time = h * ut.Time.Hour + m * ut.Time.Minute + s * ut.Time.Second
            await this.sendGm("add-worldtime", time)
            gameHelper.world.addTime(time)
        }

        public enetrGardenAnim() {
            gameHelper.planet.setCurPlanet(1005)
            let planet = gameHelper.planet.getCurPlanet()
            let map = planet.getMap(1)
            map.setProgress(0)
            map.showLandAnim = false
        }

        public guess() {
            gameHelper.planet.setCurPlanet(1001)
            let planet = gameHelper.planet.getCurPlanet()
            planet.getMap(1).setDone()
            planet.changeMap(2)
            let map = planet.getMap(2)
            map.showLandAnim = false

            let id = 11
            this.unlockGuide(id, false)
        }

        public eatGuide() {
            this.clearBuilds()
            this.unlockGuide(5)
            this.addAllMaterials(10)
        }

        public sleepGuide() {
            this.clearBuilds()
            this.unlockGuide(3)
            this.addAllMaterials(10)
        }

        public testTreeHouse() {
            this.unlockGuide(11)
            let guide = gameHelper.guide.getGuideInfo(10)
            let planet = gameHelper.planet.getSchoolPlanet()
            let map = planet.getMap(1)
            map.showLandAnim = true
            map.setProgress(26)
        }

        private clearBuilds() {
            // let carriage = gameHelper.train.getCarriages()[0]
            // carriage.themes.forEach(m => {
            //     m.builds = []
            // })
        }

        public saveCat() {
            this.unlockGuide(6)
        }

        public async unlockGuide(id, checkGuideData = true) {
            let guideList = gameHelper.guide.getGuides().slice()
            guideList.sort((a, b) => {
                return a.id - b.id
            })
            for (let guide of guideList) {
                if (guide.id < id) {
                    let step = guide.getSteps()
                    for (let { id } of step) {
                        await guide.syncStep(Number(id))
                    }
                } else if (guide.id == id) {
                }
            }
            twlog.info("unlockGuide end")
        }

        public async changeCarriage(roleId, carriageId) {
            let carriage = gameHelper.train.getCarriageById(carriageId)
            let role = gameHelper.passenger.getPassenger(roleId)
            if (!role.isCheckIn()) {
                twlog.error("先入住一下")
            }
            gameHelper.passenger.terminateRole(role.id)
            role.carriage?.roleExit(role)
            role.putToCarriage(carriage)
        }

        public unlockAllCarriagesAndBuilds() {
            let carriages = assetsMgr.getJson<TrainCfg>("Train").datas
            for (let carriage of carriages) {
                let carriageModel = gameHelper.train.addCarriage(carriage.id)
                // carriageModel.themes.forEach(t => {
                //     let builds = cfgHelper.getBuildsByTheme(t.id)
                //     for (let { id } of builds) {
                //         t.unlockBuild(id)
                //     }
                // })
            }
        }

        public setPlanetDone(id) {
            return this.setPlanetProgress(id, -1, -1)
        }

        public async setPlanetProgress(id, mapId = 1, nodeId = 1) {
            let planet = gameHelper.planet.getPlanet(id)
            await this.sendGm("set-planet", id, mapId, nodeId)
            let info = await gameHelper.user.downloadRecord()
            await gameHelper.planet.syncMoveState()
            gameHelper.planet.unlockPlanet(id)
            gameHelper.planet.setCurPlanet(id)
            let planetData = info.player.planetInfo.planets.find(p => p.id == id)
            planet.updateInfo(planetData)
        }

        public async setPlanetBranchProgress(id, mapId = 1, nodeId = 1, branchId = 1) {
            await this.sendGm("set-planet-branch", id, mapId, nodeId, branchId)
            await gameHelper.planet.syncMoveState()
            gameHelper.planet.unlockPlanet(id)
            gameHelper.planet.setCurPlanet(id)
        }

        public resetPlanet(id) {
            let planet = gameHelper.planet.getPlanet(id)
            this.setPlanetProgress(id, 1, 1)
        }

        public async reachPlanet(id) {
            await this.sendGm("reach-planet", id)
            await gameHelper.planet.syncMoveState()
            gameHelper.planet.unlockPlanet(id)
            gameHelper.planet.setCurPlanet(id)
        }

        public exitAccount() {
            gameHelper.user.exitGame()
        }

        public deletAccount() {

        }

        public capture(texture) {
            if (window.jsb) {
                // let width = texture.width;
                // let height = texture.height;
                // let data = texture.readPixels();
                // let picData = new Uint8Array(width * height * 4);
                // let rowBytes = width * 4;
                // for (let row = 0; row < height; row++) {
                //     let srow = height - 1 - row;
                //     let start = srow * width * 4;
                //     let reStart = row * width * 4;
                //     for (let i = 0; i < rowBytes; i++) {
                //         picData[reStart + i] = data[start + i];
                //     }
                // }
                // let filePath = jsb.fileUtils.getWritablePath() + imageName;
                // let success = jsb.saveImageData(picData, width, height, filePath)
                // if (success) {
                //     return filePath;
                // }
                // else {
                //     return null;
                // }
            } else {
                let width = texture.width;
                let height = texture.height;
                let canvas = document.createElement('canvas');
                canvas.width = width;
                canvas.height = height;
                let ctx = canvas.getContext('2d');
                let data = texture.readPixels();
                let rowBytes = width * 4;
                for (let row = 0; row < height; row++) {
                    let srow = height - 1 - row;
                    let imageData = ctx.createImageData(width, 1);
                    let start = srow * width * 4;
                    for (let i = 0; i < rowBytes; i++) {
                        imageData.data[i] = data[start + i];
                    }
                    ctx.putImageData(imageData, 0, row);
                }

                this.showImage(canvas)
            }
        }

        showImage(canvas) {
            var dataURL = canvas.toDataURL("image/png");
            // canvas.remove();
            var img = document.createElement("img");
            img.src = dataURL;

            let a = document.createElement('a')
            a.href = dataURL
            a.download = "test.png"
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)

            let texture = new cc.Texture2D();
            texture.initWithElement(img);

            let spriteFrame = new cc.SpriteFrame();
            spriteFrame.setTexture(texture);

            let node = new cc.Node();
            let sprite = node.addComponent(cc.Sprite);
            sprite.spriteFrame = spriteFrame;

            node.zIndex = cc.macro.MAX_ZINDEX;
            node.parent = cc.find("Canvas");
            // set position
            let width = cc.winSize.width;
            let height = cc.winSize.height;
            node.x = width / 2;
            node.y = height / 2;
            node.on(cc.Node.EventType.TOUCH_START, () => {
                node.parent = null;
                node.destroy();
            });
        }

        async testRoleAct(id, actName, params, resumeAction = true) {
            let role = gameHelper.passenger.getPassenger(id)
            let actionAgent = role.actionAgent
            let curAction = actionAgent["action"]
            let actionTree: ActionTree = curAction["actionTree"]
            actionAgent.resume()
            actionTree.terminate()
            let doFunc = async (name) => {
                let func = curAction[name]
                if (!func) {
                    console.warn("func not found", name)
                    return
                }
                await actionTree.start(func, curAction, params)
            }
            if (Array.isArray(actName)) {
                for (let name of actName) {
                    await doFunc(name)
                }
            } else if (typeof actName == 'function') {
                await actionTree.start(actName, curAction, curAction)
            } else {
                await doFunc(actName)
            }
            if (resumeAction) {
                gameHelper.passenger.startRole(id)
            }
        }

        public showJump(type: ConditionType, id?: any, num: number = 0) {
            viewHelper.showPnl('common/JumpTip', [new ConditionObj().init(type, id, num)])
        }

        async unlockBuild(id: string) {
            await this.sendGm("unlock-build", id)
            gameHelper.train.unlockBuildByReward(id)
        }

        async completeTask(id) {
            await this.sendGm("complete-task", id)
            gameHelper.task.completeTaskAndGetReward(id)
        }

        async collectStarTest(id) {
            while (1) {
                await ut.wait(1)
                let carriage = gameHelper.train.getCarriageById(id)
                if (!carriage) return
                dropItemHelper.collectAllByCarraige(carriage)
            }
        }

        public async add(type, id, count) {
            await this.sendGm("add", type, id, count)
            gameHelper.grantReward(new ConditionObj().init(type, id, count))
        }

        public async debugCopy(recordId: string, lockTime: boolean = true) {
            if (!recordId) return void console.error("请给定recordId")
            const playerId = gameHelper.user.getUid()
            const { code, notify } = await gameHelper.net.post("system/debugCopy", { recordId, playerId, lockTime: !!lockTime })
            if (code != 0) {
                return void console.error(notify)
            }
        }

        public async refreshBlackHole() {
            await this.sendGm("refresh-blackhole")
            let info = await gameHelper.user.downloadRecord()
            gameHelper.blackHole.updateInfo(info.player.blackHole)
            gameHelper.store.updateInfo(info.player.store)
        }

        public async addEquip(id?: number, lv?: number) {
            void 0 == id && (id = -1)
            void 0 == lv && (lv = 1)
            await this.sendGm("add-equip", id, lv)
            let info = await gameHelper.user.downloadRecord()
            gameHelper.equip.updateInfo(info.player.equip)
        }

        public async refreshAllWanted() {
            await this.sendGm("refresh-wanted", -1)
            gameHelper.wanted.sync()

        }

        public async clearWantedCD(index = -1) {
            await this.sendGm("cd-wanted", index)
            if (index != -1) {
                return await gameHelper.wanted.syncWanted(index)
            }
            return await gameHelper.wanted.sync()
        }

        public async refreshWanted(index = -1) {
            await this.sendGm("refresh-wanted", index)
            if (index != -1) {
                return await gameHelper.wanted.syncWanted(index)
            }
            gameHelper.wanted.sync()
        }

        public async watchWanted(cnt: number = 100, point = -1) {
            let datas = await this.watchGm("watch-wanted", cnt, point)
            let index = 0
            let ans = []
            for (let i = 0; i < cnt; i++) {
                let len = index + 5
                for (let id = 1; index < len; index++, id++) {
                    let data = datas[index]
                    let cfg = assetsMgr.getJsonData<WantedCfg>("WantedLevel", data.Level)
                    let conditions = data.Conditions
                    if (conditions.length < 4) {
                        conditions.pushArr(ut.newArray(4 - conditions.length, { Type: null, Value: null }))
                    }
                    let rewards = data.Rewards
                    if (rewards.length < 4) {
                        rewards.pushArr(ut.newArray(4 - rewards.length, { Type: null, Id: null, Num: null }))
                    }
                    ans.push(`${i + 1} ${id} ${data.Name} ${data.Conditions.map(c => `(${c.Type} ${c.Value})`).join(" ")} ${cfg.starCondition} ${data.Level} ${data.People} ${cfg.addPeople} ${data.Rewards.map(c => `(${c.Type} ${c.Id} ${c.Num})`).join(" ")}`)
                }
            }
            console.log(ans)
        }

        public async refreshStore(id: number = 1) {
            await this.sendGm("refresh-store", id)
            let info = await gameHelper.user.downloadRecord()
            gameHelper.store.updateInfo(info.player.store)
        }

        public async unlockAllCarriages() {
            await this.sendGm("all-carriage")
        }

        public async addGameTime(h, m = 0, s = 0) {
            let time = h * ut.Time.Hour + m * ut.Time.Minute + s * ut.Time.Second
            await this.sendGm("add-game-time", time)
            await gameHelper.world.syncDailyInfo()
        }

        public async chapterTest(id) {
            let monsterData = assetsMgr.getJsonData<ChapterPlanetMonsterCfg>("ChapterPlanetMonster", id)
            let lv = monsterData.monster[0].lv
            let starLv = monsterData.monster[0].starLv
            let defaultHeroMap = {
                1: [1005, 1006],
                2: [1007],
            }
            let characterDatas = assetsMgr.getJson<CharacterCfg>("Character").datas
            let passengers = []
            for (let i = 0; i < 3; i++) {
                let count = monsterData.hero[i] || 0
                let quality = i + 1
                let defaultHeroes = defaultHeroMap[quality] || []
                let qualityDatas = characterDatas.filter(c => c.quality == quality && !defaultHeroes.has(c.id)).map(c => c.id)
                let heroes = defaultHeroes.slice(0, count)
                passengers.pushArr(heroes)
                count -= heroes.length
                heroes = ut.randomArray(qualityDatas).slice(0, count)
                passengers.pushArr(heroes)
            }
            await this.setPlanetProgress(monsterData.id.split("-")[0], 1, 2)
            await Promise.all(passengers.map(async (id) => {
                await this.addPassenger(id)
                await this.setPassengerLv(id, lv)
                if (starLv > 0) {
                    await this.setPassengerStarLv(id, starLv)
                }
            }))
        }

        public async addAllDosingItems(cnt: number = 99) {
            await this.sendGm("add-all-dosing", cnt)
        }

        public async addEat(cnt: number = 99) {
            await this.addProp(18, cnt)
        }

        public async instanceSkip(cnt: number = 1) {
            const r = await this.sendGm("instance-skip", cnt)
            if (r.reply != "") {
                return void console.error(r.reply)
            }
            gameHelper.instance.currentLevel += 1
        }

        public async instanceReset() {
            const r = await this.sendGm("instance-rest")
            if (r.reply != "") {
                return void console.error("重置失败:", r.reply)
            }
            gameHelper.instance.currentLevel = 0
        }

        public addCookItem() {
            let list = cfgHelper.getTrainDosing()
            list.forEach(e => {
                this.addProp(e.id, 100)
            })
        }

        public async oreReset(level: number) {
            const r = await this.sendGm("ore-reset", level)
            if (r.reply != "") {
                return void console.error(r.reply)
            }
            // 同步数据
            level -= 1
            await gameHelper.ore.getOreLevelData(level)
            // const data = await gameHelper.ore.getOreLevelData(level)
        }

        public async orePass(level: number) {
            await gameHelper.ore.oreBattle(level - 1)
        }

        public battleSkip() {
            eventCenter.emit(EventType.BATTLE_SKIP)
        }

        public oreRoad(level: number) {
            gameHelper.ore.getARoad(level)
        }

        public test() {
            let skill = gameHelper.passenger.getPassenger(1001).getSkills()[0]
            console.log(skill.getDescStr())

            let nexSkill = new BattleSkill({ talentLv: 1 }).init(skill.getId(), skill.getLevel(), skill.getRole())
            console.log(nexSkill.getDescStr())
        }

        public oreMove(up: boolean = true) {
            eventCenter.emit(EventType.ORE_MOVE, up)
        }

        public async syncTimeStoneRecordData() {
            const r = await gameHelper.net.requestWithData(Msg.C2S_SyncTimeStoneRecordDataMessage);
            r.list?.records.forEach(l => console.table(l))
        }

        public async useTimeStoneRecordData(id: string) {
            const r = await gameHelper.net.requestWithDataWait(Msg.C2S_UseTimeStoneMessage, { id });
            if (r.code != 0) return void viewHelper.showNetError(r.code)
            gameHelper.gameRestart()
        }

        public async battleArrest(index: number, isWin: boolean = true) {
            let datas = gameHelper.arrest.getArrestList().filter(e => e.getState() != proto.ArrestState.Expired)
            let data = datas[index]
            let prePlanetId = data.getPlanetId()
            await data.setArrestBattleResult(isWin)
            if (prePlanetId != data.getPlanetId()) {
                //逃了
                viewHelper.showAlert(`逃到${data.getPlanetId()}`)
            }

        }

        public async testTransport() {
            await this.setPlanetDone(1001)
            await this.setPlanetDone(1005)
            await this.setPlanetDone(1006)
            await this.setPlanetDone(1009)
            await this.setPlanetDone(1007)
            await this.unlockAllCarriages()
            await this.trainItemBuilt(0, 10)
            await this.refreshTransport()
            await gameHelper.addDiamond(10000)
            await gameHelper.jackpot.commonExtract(2)
            await this.setPassengerLv(0, 40)
            gameHelper.gameRestart()
        }

        public async testDailyTask() {
            await this.setPlanetDone(1005)
            await this.setPlanetDone(1006)
            await this.setPlanetDone(1009)
            await this.setPlanetDone(1001)
            await this.unlockAllCarriages()
            await this.trainItemBuilt(0, 10)
            await this.refreshDaily()
            await this.setTool(20, -1)
            gameHelper.gameRestart()
        }

        public async testArrest() {
            await this.setPlanetDone(1001)
            await this.setPlanetDone(1005)
            await this.setPlanetDone(1006)
            await this.setPlanetDone(1009)
            await this.setPlanetDone(1007)
            await this.setPlanetDone(1008)
            await this.setPlanetDone(1018)
            await this.setPlanetDone(1014)
            await this.addDiamond(10000)
            await gameHelper.jackpot.commonExtract(2)
            await this.setPassengerLv(0, 60)
            await this.refreshArrest()
            gameHelper.gameRestart()
        }

        public async refreshDaily() {
            await this.sendGm("refresh-daily")
            let info = await gameHelper.user.downloadRecord()
            gameHelper.dailyTask.updateInfo(info.player.dailyTask)
            gameHelper.collect.updateInfo(info.player.collect)
        }

        public async refreshArrest() {
            await this.sendGm("refresh-arrest")
        }

        public addProfiles(characterId: number) {
            let list = assetsMgr.getJson<CharacterProfileCfg>('CharacterProfile').datas.filter(t => t.characterId == characterId)
            list.forEach(t => {
                this.add(30, t.id, 1)
            })
        }

        public async transportSetDone(index: number) {
            let transport = gameHelper.transport.getTransportByIndex(index)
            if (!transport) return void console.error("错误的index")
            await this.sendGm("transport-set-done", index)
            transport.setState(proto.TransportDataState.Done)
            eventCenter.emit(EventType.TRANSPORT_FINISH, transport)
        }

        public async buyEquip() {
            let passengers = gameHelper.passenger.getPassengers()
            let datas = assetsMgr.getJson<EquipCfg>("Equip").datas
            for (let data of datas) {
                if (!passengers.has("id", data.roleId)) continue
                await gameHelper.equip.buy(data.id, 1, data.index)
            }
        }

        // 重置pvp挑战次数
        public async resetPvpTicket() {
            await this.sendGm("reset-pvp-ticket")
            gameHelper.pvp.resetTicket()
        }

        public startGuide(id: number, progress = 0) {
            gameHelper.guide.stateLoad = 2
            gameHelper.guide.guideId = 0
            let guide = gameHelper.guide.getGuideInfo(id)
            guide.clean()
            gameHelper.guide.start(id, progress)
        }

        public testAllPlanetProfile() {
            const has = gameHelper.planetArchives.getList()
            // @ts-ignore
            Object.keys(cfgHelper.planetProfileMap).forEach(i => {
                // 资料是独一份  排除 已经拥有的和已经激活的
                if (!!has.find(h => h.id == Number(i))) return
                // @ts-ignore
                const planet = gameHelper.planet.getPlanet(cfgHelper.planetProfileMap[i].planetId)
                if (!planet) return
                if (planet.isUnlockProfile(Number(i))) return
                this.add(31, Number(i), 1)
            })
        }

        public async setExploreDone(planetId?: number) {
            if (!planetId) {
                planetId = gameHelper.planet.getCurPlanet().getId()
            }
            const info = gameHelper.deepExplore.getExploreInfo(planetId)
            if (!info) return void console.error("星球上没有探索任务！")
            await this.sendGm("set-done-explore", planetId)
            info.endTime = 0
        }

        public async resetProfileBranch(num: number = 1) {
            await this.sendGm("reset-profile-branch", num)
            let info = await gameHelper.user.downloadRecord()
            gameHelper.profileBranch.data = info.player.profileBranch
            gameHelper.profileBranch.init()
        }

        public async changeProfileBranchEnergy(num: number = 1) {
            await this.sendGm("change-profile-branch-energy", num)
            gameHelper.profileBranch.changeEnergy(num)
        }

        public async setDailyTaskDone() {
            await this.sendGm("set-daily-task-done")
            let info = await gameHelper.user.downloadRecord()
            gameHelper.bag.data = info.player.bag
            gameHelper.equip.data = info.player.equip
            gameHelper.bag.init()
            gameHelper.equip.init()
            gameHelper.dailyTask.updateInfo(info.player.dailyTask)
        }

        public async refreshCollectMap() {
            await this.sendGm("refresh-collect-map")
            let info = await gameHelper.user.downloadRecord()
            gameHelper.collect.updateInfo(info.player.collect)
        }

        public debugCollectMine(debug: boolean = true) {
            let wind = mc.currWind.Component(CollectWindCtrl)
            if (wind) {
                wind.debugHideInterference(debug)
            }
        }

        public async doneItemTime(id: number = -1) {
            await this.sendGm("done-item-time", id)
            // @ts-ignore
            let items = gameHelper.bag.props.filter(e => e.id == id || id == -1)
            items.forEach(e => {
                // @ts-ignore
                e.updateEndTime(0)
            })
        }

        public async refreshTrainDailyTask() {
            await this.sendGm("refresh-train-daily-task")
            await gameHelper.trainDailyTask.syncAll()
        }

        public async setTrainDailyTaskDone(index: number = -1) {
            await this.sendGm("set-train-daily-task-done", index)
            await gameHelper.trainDailyTask.syncAll(index)
        }

        public showCarriageMapDebug(show) {
            eventCenter.emit(EventType.SHOW_CARRIAGE_DEBUG_MAP, show)
        }

        public async refreshTrainActivity(day: number = 0) {
            await this.sendGm("refresh-train-activity", day)
            let info = await gameHelper.user.downloadRecord()
            gameHelper.trainActivity.data = info.player.trainActivity
            gameHelper.trainActivity.init()
        }

        // 增加列车建设材料
        public async addTrainMaterial() {
            // 材料
            assetsMgr.getJson<ItemCfg>("Item").datas
                .filter(item => item.type == ItemType.MATERIAL)
                .forEach(item => {
                    this.addProp(item.id, 500)
                })
            // 电
            this.addProp(ItemID.ELECTRIC, 99999999)
        }

    }
    window["debugHelper"] = new DebugHelper()
}


